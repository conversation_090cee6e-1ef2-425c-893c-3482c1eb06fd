import { useRef, useState } from 'react'
import { useToast } from '@/components/ui/shad/use-toast'
import { useTranslation } from 'react-i18next'

/** 实时获取的摄像头状态
 */
export enum CameraCurrentStatus {
  success = 'success',
  error = 'error',
  loading = 'loading',
}
/** 摄像头的组件
 * 摄像头分辨率备注：摄像头存在截取不一致的情况，采用固定3:4的比例，canvas按照实际分辨率截取的方式
 */
export const useCamera = ({
  videoId,
  canvasId,
}: {
  videoId: string
  canvasId: string
}) => {
  const { toast } = useToast()
  const { t } = useTranslation()
  const camTrack = useRef<any>(null)
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const [cameraCurrentStatus, setCameraCurrentStatus] =
    useState<CameraCurrentStatus>(CameraCurrentStatus.loading)
  /** 摄像头实际分辨率 */
  const [realVideoSize, setRealVideoSize] = useState<{
    width: number
    height: number
  }>({
    width: 600,
    height: 800,
  })
  // 初始化相机成功回调
  const handleCamSuccess = (stream: MediaStream) => {
    const camVideo = document.getElementById(videoId) as HTMLVideoElement
    videoRef.current = camVideo
    camTrack.current = stream.getTracks()[0]

    console.log('camTrack.current ', camTrack.current)
    if ('srcObject' in camVideo) {
      console.log('stream', stream)
      camVideo.srcObject = stream
      // 视频元数据加载完成，视频被用户看到（比较卡的机器，只有onloadedmetadata加载后，视频才展示）
      camVideo.onloadedmetadata = function () {
        console.log('实际分辨率: ', camVideo.videoWidth, camVideo.videoHeight)
        // 实际视频分辨率
        setRealVideoSize({
          width: camVideo.videoWidth,
          height: camVideo.videoHeight,
        })
        setCameraCurrentStatus(CameraCurrentStatus.success)
      }
      console.log(
        videoId + 'camera-setting---->',
        stream?.getVideoTracks()[0]?.getSettings()
      )
    } else {
      ;(camVideo as any).src =
        (window.URL && window.URL.createObjectURL(stream as any)) || stream
    }
    camVideo.play()
  }
  const handelCamError = (error: Error | DOMException) => {
    setCameraCurrentStatus(CameraCurrentStatus.error)
    console.error('摄像头调用报错：', error)
    toast({
      description: t(
        '抱歉，摄像头暂时出现问题。请您通过“上传图片”的方式继续操作。感谢您的理解与配合！'
      ),
    })
  }
  // 初始化相机
  const initCamera = ({
    videoOption = {
      width: 1920,
      height: 1080,
      /** 固定比例的方式 */
      aspectRatio: { exact: 3 / 4 },
    },
  }: {
    /** 视频流参数 */
    videoOption?: {
      width?: number
      height?: number
      aspectRatio?: { exact: number }
    }
  } = {}) => {
    console.log('videoOption', videoOption)
    getUserMediaToPhoto(
      {
        video: videoOption,
      },
      handleCamSuccess,
      handelCamError
    )
  }

  // 调用媒体设备
  const getUserMediaToPhoto = (
    constraints: MediaStreamConstraints,
    success: (stream: MediaStream) => void,
    error?: (error: Error | DOMException) => void
  ) => {
    if (navigator.mediaDevices.getUserMedia) {
      console.log(
        'navigator.mediaDevices.getUserMedia',
        navigator.mediaDevices.getUserMedia
      )
      // 最新标准API
      navigator.mediaDevices
        .getUserMedia(constraints)
        .then(success)
        .catch(error)
    } else if ((navigator as any).webkitGetUserMedia) {
      console.log(
        'navigator.webkitGetUserMedia',
        (navigator as any).webkitGetUserMedia
      )
      ;(navigator as any).webkitGetUserMedia(constraints, success, error)
    } else if ((navigator as any).mozGetUserMedia) {
      console.log(
        'navigator.mozGetUserMedia',
        (navigator as any).mozGetUserMedia
      )
      // firefox浏览器
      ;(navigator as any).mozGetUserMedia(constraints, success, error)
    } else if ((navigator as any).getUserMedia) {
      console.log('navigator.getUserMedia', (navigator as any).getUserMedia)
      // 旧版API
      ;(navigator as any).getUserMedia(constraints, success, error)
    }
  }
  const changeVideoStatus = () => {
    if (!videoRef.current) return

    if (videoRef.current.paused) {
      videoRef.current.play()
    } else {
      videoRef.current.pause()
    }
  }
  const closeCamera = () => {
    console.log('camTrack.current-destory', camTrack.current)
    if (camTrack.current) {
      camTrack.current?.stop()
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
  }
  const handleCatchPhoto = () => {
    return new Promise(async (resolve, reject) => {
      try {
        changeVideoStatus()
        const camVideo = document.getElementById(videoId) as HTMLVideoElement
        const camCanvas = document.getElementById(canvasId) as HTMLCanvasElement
        const camCtx = camCanvas.getContext('2d')
        console.log('realVideoSize', realVideoSize)
        // canvas采用实际分辨率
        const camWidth = realVideoSize.width
        const camHeight = realVideoSize.height

        camCanvas.width = camWidth
        camCanvas.height = camHeight

        camCtx?.clearRect(0, 0, camWidth, camHeight)
        // 镜像翻转画布
        camCtx?.translate(camCanvas.width, 0)
        camCtx?.scale(-1, 1)
        camCtx?.drawImage(camVideo, 0, 0, camWidth, camHeight)

        console.time('toBlob')
        const blobImg = await new Promise(resolve => {
          camCanvas.toBlob(
            blob => {
              resolve(blob)
            },
            'image/jpeg',
            1
          ) // 指定类型和质量
        })
        console.timeEnd('toBlob')

        resolve(blobImg)
      } catch (error) {
        reject(error)
      }
    })
  }
  return {
    initCamera,
    handleCatchPhoto,
    changeVideoStatus,
    closeCamera,
    /** 摄像头实时状态 */
    cameraCurrentStatus,
    realVideoSize,
  }
}
