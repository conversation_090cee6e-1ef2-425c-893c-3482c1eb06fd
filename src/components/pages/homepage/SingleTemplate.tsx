import { MirrorSexEnum } from '@/graphqls/types'
import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './SingleTemplate.module.css'
import { ThemeDetail } from '@/apis/types'
import { useAtomValue, useSetAtom } from 'jotai'
import { screenOrientationAtom, isShowThemeDetailModalAtom } from '@/stores'

/** 单个模版 */
function SingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
  activeGender,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const setThemeDetailModalOpen = useSetAtom(isShowThemeDetailModalAtom)
  console.log('singleTemplate:', item)
  return (
    <div
      className={classnames(
        className,
        styles.template,
        isMultiple && styles.multiple,
        // 'flex flex-col cursor-pointer',
        {
          [styles.active]: active,
        }
      )}
      onClick={onSelect}
    >
      {item.name && (
        <div className="maze-theme-title-bg w-full h-[7.75rem] text-[2.5rem] leading-[7.75rem] font-semibold px-[5rem] rounded-3xl text-white absolute left-0 right-0 bottom-0 text-center text-ellipsis text-nowrap overflow-hidden ipad:h-[5.38rem] ipad:leading-[5.38rem] ipad:text-[2rem]">
          {item.name}
        </div>
      )}
      {item.female_model_count || item.male_model_count ? (
        <div
          onClick={() => setThemeDetailModalOpen(true)}
          className="absolute cursor-pointer text-[2rem] w-14 h-14 text-white leading-none left-5 bottom-8 rounded-full flex items-center justify-center ipad:scale-[0.8] ipad:left-4 ipad:bottom-4 maze-badge-default"
        >
          <span>
            {activeGender === MirrorSexEnum.FEMALE
              ? item.female_model_count
              : item.male_model_count}
          </span>
        </div>
      ) : (
        <div className="absolute no-data-found"></div>
      )}
      <MyImage
        src={
          activeGender === MirrorSexEnum.FEMALE
            ? item?.cover_image_female
            : item?.cover_image
        }
        tag="v800"
        className={classNames('rounded-[3rem] ipad:rounded-[2rem] ', [
          screenOrientation.isLandScape
            ? 'h-[12.23vh]'
            : 'h-[36.28vh] ipad:h-[35vh] phone:h-[36.28dvh]',
        ])}
        imgClassName="object-cover"
      />
      {/* <div
        className={classnames(
          'font-bold text-xl mt-4 line-clamp-1 text-center',
          {
            'text-neutral-50': !active,
            'text-gradient-primary': active,
          }
        )}
      >
        {item?.name}
      </div> */}
    </div>
  )
}

export default SingleTemplate
