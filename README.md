## 开发指南

[详情](https://alidocs.dingtalk.com/i/nodes/D1YKdxGX7EqVQAG94dZNVe4QrZk95AzP)

## 大屏缩放方案

1. ios 系统：采用 scale 缩放，因有 zoom 兼容问题

2. 其他系统：采用 zoom 缩放 （支持模版撑满展示骚操作）

```
pnpm i

pnpm run dev

http://localhost:16661?device-token=eyJhbGciOiJIUzI1NiJ9.eyJkZXZpY2VJZCI6MTEyMCwiZGV2aWNlU2Vzc2lvbklkIjoxNDEsIm1lcmNoYW50Tm8iOiIxODA0MDQ4OTkwMjQyMjIyMDgwIiwiZGV2aWNlVHlwZUNvZGUiOjIsImlhdCI6MTcyMTg3MjAwNCwic3ViIjoiMTEyMCJ9.zzGJWLyk8aGO8-HtDMLBUHIjiKBFIoEFb_HNNPNrZr8

// device-token 会由app注入到webview cookie中，以上仅用于本地调试

```

```
maze-token: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozOSwiaXNzIjoiTWF6ZVN0dWRpby1BUEkiLCJzdWIiOiJNYXplU3R1ZGlvIiwiYXVkIjpbIkFQSSJdLCJleHAiOjE3NDU0ODc2OTAsIm5iZiI6MTc0Mjg5NTY5MCwiaWF0IjoxNzQyODk1NjkwLCJqdGkiOiI2N2UyN2E0YTU5ZGY2NDlkN2M0YzRmMmQifQ.4a5oCPKo_Nrxb-0SHlRaPtGn8VG5KqMaUvcfrb7OFOJoVlukZSGBlL7IWtZ_FJEBBIFFXeFTnGXzLjA0rSOrCg


http://***********:16661/?device-token=eyJhbGciOiJIUzI1NiJ9.eyJkZXZpY2VJZCI6MTIyMywiZGV2aWNlU2Vzc2lvbklkIjo2NjcsIm1lcmNoYW50Tm8iOiIwMTg1MTMwIiwiZGV2aWNlVHlwZUNvZGUiOjIsImlhdCI6MTc0MDAzNzAzNywic3ViIjoiMTIyMyJ9.U0SNDnEgQNQbwXot9x679Id6PoUog4nDr8NX2_LLi6o&token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMjM5LCJpc3MiOiJNYXplU3R1ZGlvLUFQSSIsInN1YiI6Ik1hemVTdHVkaW8iLCJhdWQiOlsiQVBJIl0sImV4cCI6MTc0OTk3NDE5NywibmJmIjoxNzQ3MzgyMTk3LCJpYXQiOjE3NDczODIxOTcsImp0aSI6IjY4MjZlZmI1NTRjOTYzMDE4NTRlMDQwZiJ9.FjmOtlb7jQxQ0S1KEj3TEAgy91SegS_JFlA_qbinLeHb5RPRHK3M8h4goRtA1wtA1vZv-W5esFnBY6NvdOzhMA
```

### 约定
已适配的屏幕（需求不断叠加导致适配维护困难，很多先期未考虑的问题，后抽时间移除hack，统一一下）
  横屏（1920 x 1080）
  竖屏 （1080 x 1920）
  ipad tailwindcss ipad:xxx
  webapp tailwindcss phone:xxx
  iphone isIphone()@ua.ts 来判断，以此来区别webapp，覆盖样式

