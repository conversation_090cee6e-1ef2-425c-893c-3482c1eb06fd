import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { VideoTemplateList } from './VideoTemplateList'
import MyTab from '@/components/ui/MyTab'
import { DrawItemFragment, PopulationCategory } from '@/graphqls/types'
import classNames from 'classnames'
import { CreateBtn } from './CreateBtn'
import { ActiveTemplateItem } from './const'
import { useAtom, useAtomValue } from 'jotai'
import {
  canShowVideoAtom,
  isTaskTypeSelectedAtom,
  resourceVideoTemplateAtom,
  screenOrientationAtom,
} from '@/stores'
import { type Swiper as SwiperRef } from 'swiper'
import BackToHome from '@/components/business/BackToHome'
import { uniqueBy } from '@/utils'
import { useBridge } from '@/hooks/useBridge'
import { ThemeDetail } from '@/apis/types'

export const VideoFramework = () => {
  const [resourceVideoTemplate] = useAtom(resourceVideoTemplateAtom)
  const [, setIsTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [, setCanShowVideo] = useAtom(canShowVideoAtom)
  const { hiddenVideo } = useBridge()

  const [activeTemplate, setActiveTemplate] = useState<
    ActiveTemplateItem | undefined | null
  >()
  const [activeGender, setActiveGender] = useState(PopulationCategory.FEMALE)

  const swiperRef = useRef<SwiperRef>()

  const handleFilterTemplateList = useCallback(
    (activeGender: PopulationCategory) => {
      const template = (
        uniqueBy(
          resourceVideoTemplate
            ?.map(it => {
              return it.itemList || []
            })
            ?.flat(),
          it => it.id
        ) || []
      )?.filter(it =>
        it?.populationV2?.includes(activeGender as PopulationCategory)
      ) as ThemeDetail[]

      return template
    },
    [resourceVideoTemplate]
  )

  const selectTemplateList = useMemo(() => {
    const template = handleFilterTemplateList(activeGender)

    if (!template?.length) {
      return []
    }
    /** 备注：轮播必须要5个slide才展示正常 */
    while (template.length < 5) {
      template.push(...template)
    }
    return template
  }, [resourceVideoTemplate, activeGender])

  useEffect(() => {
    setActiveTemplate(selectTemplateList[0])
  }, [selectTemplateList])

  useEffect(() => {
    // 切换人群slide回到第一个
    swiperRef.current?.slideToLoop(0, 0)
  }, [activeGender])

  useEffect(() => {
    setCanShowVideo(true)

    return () => {
      setCanShowVideo(false)
      console.log('hiddenVideo2')
      hiddenVideo()
    }
  }, [])

  const crowd = useMemo(() => {
    return [
      {
        label: '女性',
        value: PopulationCategory.FEMALE,
        visible: !!handleFilterTemplateList(PopulationCategory.MALE).length,
      },
      {
        label: '儿童',
        value: PopulationCategory.CHILD,
        visible: !!handleFilterTemplateList(PopulationCategory.CHILD).length,
      },
      {
        label: '老年',
        value: PopulationCategory.ELDERLY,
        visible: !!handleFilterTemplateList(PopulationCategory.ELDERLY).length,
      },
      {
        label: '男性',
        value: PopulationCategory.MALE,
        visible: !!handleFilterTemplateList(PopulationCategory.MALE).length,
      },
      {
        label: '双人',
        value: PopulationCategory.DOUBLE,
        visible: !!handleFilterTemplateList(PopulationCategory.DOUBLE).length,
      },
    ]
  }, [])

  return (
    <>
      <BackToHome
        onCustomBack={() => {
          setIsTaskTypeSelected(false)
        }}
      />
      {screenOrientation.isPortrait ? (
        <div className="h-full flex flex-col justify-center">
          <div className="relative z-10 flex justify-center">
            <MyTab
              type="capsule"
              items={crowd.filter(it => it.visible)}
              activeKey={activeGender}
              setActiveKey={val => {
                setActiveGender(val)
              }}
              className={classNames('whitespace-nowrap !inline-flex', {
                '!bg-transparent !mr-0 !justify-center !flex':
                  screenOrientation.isPortrait,
              })}
            />
          </div>
          <VideoTemplateList
            swiperRef={swiperRef}
            activeTemplate={activeTemplate}
            selectTemplateList={selectTemplateList}
            setActiveTemplate={setActiveTemplate}
          />
          <CreateBtn activeTemplate={activeTemplate} isPortrait activeGender={activeGender} />
        </div>
      ) : (
        <div className="py-14">
          <div className="relative z-10 flex justify-center">
            <MyTab
              type="default"
              items={crowd.filter(it => it.visible)}
              activeKey={activeGender}
              setActiveKey={val => {
                setActiveGender(val)
              }}
              className={classNames('whitespace-nowrap !inline-flex')}
            />
          </div>
          <VideoTemplateList
            swiperRef={swiperRef}
            activeTemplate={activeTemplate}
            selectTemplateList={selectTemplateList}
            setActiveTemplate={setActiveTemplate}
          />
          <CreateBtn activeTemplate={activeTemplate} activeGender={activeGender} />
        </div>
      )}
    </>
  )
}
