import {
  Direction,
  FitNumber,
  MirrorAiTaskDetailFragment,
  PrintTemplateSize,
} from '@/graphqls/types'

export enum CameraStatus {
  /** 不存在 */
  NONE = 'NONE',
  /** 可用 */
  AVAILABLE = 'AVAILABLE',
  /** 不可用 */
  UNAVAILABLE = 'UNAVAILABLE',
}

export enum PrinterStatus {
  /** 不存在 */
  NONE = 'NONE',
  /** 可用 */
  AVAILABLE = 'AVAILABLE',
  /** 不可用 */
  UNAVAILABLE = 'UNAVAILABLE',
  /** 打印机缺纸 */
  PAPER_LACK = 'PAPER_LACK',
  /** 打印纸充足 */
  PAPER_PLENTY = 'PAPER_PLENTY',
  /** 打印机缺墨 */
  INK_LACK = 'INK_LACK',
  /** 打印墨充足 */
  INK_PLENTY = 'INK_PLENTY',
}

export enum NetworkStatus {
  /** 网络正常 */
  ONLINE = 'ONLINE',
  /** 网络异常 */
  OFFLINE = 'OFFLINE',
  /** 弱网 */
  WEAKNESS = 'WEAKNESS',
}

/** 摄像头类型 */
export enum CameraTypeEnum {
  /** 普通摄像头 */
  NORMAL = 'NORMAL',
  /** 佳能相机 */
  CANON = 'CANON',
}
/** 佳能相机 - 拍照动作 */
export enum CanonActionEnum {
  /** 按下快门 */
  PRESS = 'PRESS',
  /** 快门释放 */
  RELEASE = 'RELEASE',
  /** wifi连接相机失败 */
  NONE = 'NONE',
}

export interface ScreenOrientation {
  /** 横屏 */
  isLandScape: boolean
  /** 竖屏 */
  isPortrait: boolean
}

export interface MyMirrorAiTask extends MirrorAiTaskDetailFragment {
  print?: number
  price?: number
  fitNumber?: FitNumber
  direction?: Direction
  support3DModeling?: boolean
  categoryInfo?: [
    {
      name: string
    },
  ]
}

export interface Printer {
  /** 打印机状态，默认无模块 */
  printerStatus: PrinterStatus
  /** 打印机打印纸剩余*/
  printerPaperNum: number
  /** 打印机墨水状态 */
  printerInkStatus: PrinterStatus
  type: PrintTemplateSize
}

/** 机器状态 */
export interface MachineStatus {
  /** 唯一标识 */
  uuid: string | null
  /** 屏幕宽 */
  screenWidth: number
  /** 屏幕高 */
  screenHeight: number
  /** 摄像头状态，默认无模块 */
  cameraStatus: CameraStatus
  /** 摄像头类型 */
  cameraType: CameraTypeEnum
  /** 打印机状态，默认无模块 */
  printerStatus: PrinterStatus
  /** 打印机打印纸剩余*/
  printerPaperNum: number
  /** 打印机墨水状态 */
  printerInkStatus: PrinterStatus
  /** 网络状态 */
  networkStatus: NetworkStatus
  /** 支持多台打印机 */
  printers: Printer[] | null
  hasSecondaryScreen?: boolean
}

/** 主题 */
export interface ThemeConfigProps {
  bgColor: string | undefined | null // 背景色
  bgUrl: string | undefined | null // 横版背景图
  portraitBgUrl: string | undefined | null // 竖版背景图
  textColor: string | undefined | null // 背景下的文字颜色
  themeColor: string | undefined | null // 主题色/按钮背景色
  buttonTextColor: string | undefined | null // 按钮文字颜色
  logoUrl: string | undefined | null // logo
}
