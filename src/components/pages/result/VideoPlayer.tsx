import { MyImage } from '@/components/ui/MyImage'
import {
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from 'react'
import { publicPreLoadSourceObj } from '@/configs/source'
import { useAtom } from 'jotai'
import { currentPlayingVideoIdAtom } from '@/stores'

interface Props {
  poster: string
  videoUrl: string
  id?: string // 视频播放器唯一标识
}

export interface VideoPlayerRef {
  play: () => void
  pause: () => void
  isPlaying: () => boolean
}

export const VideoPlayer = forwardRef<VideoPlayerRef, Props>(
  ({ poster, videoUrl, id = `video-${Date.now()}-${Math.random()}` }, ref) => {
    const videoRef = useRef<HTMLVideoElement>(null)
    const [playing, setPlaying] = useState(false)
    const [currentPlayingVideoId, setCurrentPlayingVideoId] = useAtom(
      currentPlayingVideoIdAtom
    )

    // 外部控制接口
    useImperativeHandle(ref, () => ({
      play: () => handlePlay(),
      pause: () => handlePause(),
      isPlaying: () => playing,
    }))

    const handlePlay = async () => {
      console.log('handlePlay called, videoRef.current:', videoRef.current)
      console.log('video element exists:', !!videoRef.current)

      if (currentPlayingVideoId && currentPlayingVideoId !== id) {
        // 暂停其他正在播放的视频
        setCurrentPlayingVideoId(null)
      }
      setCurrentPlayingVideoId(id)
      setPlaying(true)

      if (videoRef.current) {
        try {
          console.log('Attempting to play video...')
          const playPromise = videoRef.current.play()
          if (playPromise !== undefined) {
            await playPromise
            console.log('Video play successful')
          }
        } catch (error) {
          console.error('Video play failed:', error)
          setPlaying(false)
          if (currentPlayingVideoId === id) {
            setCurrentPlayingVideoId(null)
          }
        }
      } else {
        console.error('videoRef.current is null!')
      }
    }

    const handlePause = () => {
      setPlaying(false)
      videoRef.current?.pause()
      if (currentPlayingVideoId === id) {
        setCurrentPlayingVideoId(null)
      }
    }

    // 监听全局播放状态变化
    useEffect(() => {
      if (currentPlayingVideoId !== id && playing) {
        // 其他视频开始播放，暂停当前视频
        setPlaying(false)
        videoRef.current?.pause()
      }
    }, [currentPlayingVideoId, id, playing])

    useEffect(() => {
      const video = videoRef.current
      if (!video) return

      const handlePlaying = () => {
        console.log('video playing:', id)
        setPlaying(true)
      }

      const handleEnded = () => {
        console.log('video ended:', id)
        setPlaying(false)
        if (currentPlayingVideoId === id) {
          setCurrentPlayingVideoId(null)
        }
      }

      const handlePause = () => {
        console.log('video paused:', id)
        setPlaying(false)
      }

      video.addEventListener('playing', handlePlaying)
      video.addEventListener('ended', handleEnded)
      video.addEventListener('pause', handlePause)

      return () => {
        video.removeEventListener('playing', handlePlaying)
        video.removeEventListener('ended', handleEnded)
        video.removeEventListener('pause', handlePause)
      }
    }, [videoUrl, id, currentPlayingVideoId])

    return (
      <>
        {/* 视频元素始终存在，通过样式控制显示/隐藏 */}
        <video
          ref={videoRef}
          src={videoUrl}
          poster={poster}
          controls={false}
          playsInline
          muted={false}
          className={`w-full h-full rounded-2xl ${playing ? 'block' : 'hidden'}`}
        />

        {/* 封面图和播放按钮，只在未播放时显示 */}
        {!playing && (
          <>
            <MyImage
              src={poster}
              className="w-full h-full rounded-2xl"
              isAppCache={false}
            />
            <div
              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2  w-[6rem] h-[6rem] cursor-pointer"
              onClick={handlePlay}
            >
              <img
                src={publicPreLoadSourceObj.play}
                className="w-full h-full"
                alt="播放"
              />
            </div>
          </>
        )}
      </>
    )
  }
)
