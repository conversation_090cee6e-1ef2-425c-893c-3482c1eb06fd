import { Picture<PERSON>ramework } from '@/components/pages/homepage/PictureFramework'
import { TaskTypeSelect } from '@/components/pages/homepage/TaskTypeSelect'
import { VideoFramework } from '@/components/pages/homepage/VideoFramework'
import { MyContainer } from '@/components/ui/MyContainer'
import {
  AiTaskType,
  GetResourceHashDocument,
  GetResourceHashQuery,
} from '@/graphqls/types'
import { useDevice } from '@/hooks/useDevice'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useMerchant } from '@/hooks/useMerchant'
import { usePreloadResource } from '@/hooks/usePreloadResource'
import { useResetUserAtom } from '@/hooks/useResetUserAtom'
import {
  isTaskTypeSelectedAtom,
  isSupportVideoAtom,
  resourceHashAtom,
  taskTypeAtom,
} from '@/stores'
import { useAtom } from 'jotai'
import { useEffect } from 'react'

function Index() {
  const [taskType] = useAtom(taskTypeAtom)
  const [isSupportVideo] = useAtom(isSupportVideoAtom)
  const [isTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)
  const [resourceHash] = useAtom(resourceHashAtom)
  const { resetAtom } = useResetUserAtom()
  const { fetchResources } = usePreloadResource()
  const { getDeviceInfo, updateDeviceInfo } = useDevice()
  const { getMerchantInfo } = useMerchant()

  const getResourceHash = useImperativeQuery<GetResourceHashQuery>(
    GetResourceHashDocument
  )

  /** 对比hash，更新资源包 */
  useEffect(() => {
    const timer = setInterval(async () => {
      try {
        const hashRes = await getResourceHash()
        if (hashRes?.data?.deviceQuery?.templateHash === resourceHash) return

        fetchResources()
      } catch (err) {
        console.log(err, 'hash')
      }
    }, 60 * 1000)
    return () => {
      clearInterval(timer)
    }
  }, [resourceHash])

  useEffect(() => {
    // 后台配置的设备信息
    getDeviceInfo().catch(() => {})
    // 商户信息
    getMerchantInfo().catch(() => {})
    // 更新机器状态
    updateDeviceInfo()
    // 回到模板页后，重置用户数据atom
    resetAtom()
  }, [])

  return (
    <MyContainer>
      {isSupportVideo && !isTaskTypeSelected ? (
        <TaskTypeSelect />
      ) : taskType === AiTaskType.DRAW ? (
        <PictureFramework />
      ) : (
        <VideoFramework />
      )}
    </MyContainer>
  )
}

export default Index
