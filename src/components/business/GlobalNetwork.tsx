import { MyModal } from '@/components/ui/MyModal'
import { useBridge } from '@/hooks/useBridge'
import { useEffect, useState } from 'react'
import CountDown from './CountDown'
import { AFTER_PAY_ROUTES } from '@/configs'
import { useLocation, useNavigate } from 'react-router-dom'
import { QuestionModal } from './QuestionModal'
import { isMirror } from '@/utils'
import { canShowVideoAtom } from '@/stores'
import { useAtom } from 'jotai'

export const GlobalNetwork: React.FC<{
  onError: () => void
  onOk: () => void
}> = ({ onError, onOk }) => {
  const [open, setOpen] = useState(false)
  const { networkStatus } = useBridge()
  const location = useLocation()
  const navigator = useNavigate()
  const [, setCanShowVideo] = useAtom(canShowVideoAtom)

  const [questionOpen, setQuestionOpen] = useState(false)

  const onFinish = () => {
    /** 是否是付款后的页面 */
    const isAfterPayRoute = AFTER_PAY_ROUTES.includes(location.pathname)

    setOpen(false)

    if (isAfterPayRoute) {
      setQuestionOpen(true)
    } else {
      navigator('/')
    }
  }

  useEffect(() => {
    if (isMirror()) {
      networkStatus({
        callback: (status: boolean) => {
          console.log('networkStatus callback: ', status)
          if (status === false) {
            setOpen(true)
            setCanShowVideo(false)
            onError()
          } else {
            setOpen(false)
            setCanShowVideo(true)
            onOk()
          }
        },
      })
    }
  }, [])

  return (
    <>
      <MyModal
        open={open}
        title="网络异常"
        content={
          <span className="text-center text-lg leading-[42px]">
            设备网络出现了一点小问题
          </span>
        }
        showCancelButton={false}
        showOkButton={false}
        footerExtra={
          <div className="text-center text-neutral-50 text-lg leading-[42px]">
            正在尝试重新连接{' '}
            <span className="text-gradient-primary">
              <CountDown value={120} onFinish={onFinish} />s
            </span>
          </div>
        }
      />
      <QuestionModal open={questionOpen} setOpen={setQuestionOpen} />
    </>
  )
}
