import { MyImage } from '@/components/ui/MyImage'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import { MyMirrorAiTask } from '@/stores/types'
import { cn } from '@/utils/shad'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { publicPreLoadSourceObj } from '@/configs/source'
import { useGlobalVideoPlayer } from '@/hooks/useGlobalVideoPlayer'

interface Props {
  curImg?: MyMirrorAiTask
  curVideo?: MyMirrorAiTask
  imgClassName?: string
  className?: string
}

export function MainImage({
  curImg,
  curVideo,
  imgClassName,
  className,
}: Props) {
  const [focus3d, setFocues3d] = useState(false)
  const { t } = useTranslation()
  const { openVideoPlayer, openImageViewer } = useGlobalVideoPlayer()

  useEffect(() => {
    setFocues3d(false)
  }, [curImg?.id])

  const handlePlay = (e: React.MouseEvent<HTMLImageElement>) => {
    e.stopPropagation()
    if (curVideo) {
      console.log('🎬 MainImage: 打开视频播放器', {
        video: curVideo.id,
        poster: curImg?.resultUrl,
      })
      openVideoPlayer(curVideo, curImg?.resultUrl)
    }
  }

  const handleImageClick = (e: React.MouseEvent<HTMLImageElement>) => {
    e.stopPropagation()
    if (curImg?.resultUrl) {
      console.log('🖼️ MainImage: 打开图片查看器', {
        imageUrl: curImg.resultUrl,
      })
      openImageViewer(curImg.editResultUrls?.[2] ?? curImg.resultUrl)
    }
  }

  return (
    <div
      className={cn(
        'w-full flex justify-center flex-col text-center rounded-2xl text-[1.5rem] font-bold leading-[2rem]',
        className
      )}
    >
      {curImg?.status === MirrorAiTaskStatus.SUCCESS ? (
        <div className="w-full h-full relative">
          <MyImage
            src={curImg?.editResultUrls?.[2] ?? curImg?.resultUrl}
            tag="v800"
            className="w-full h-full rounded-2xl cursor-pointer"
            isAppCache={false}
            imgClassName={imgClassName}
          />
          {curVideo?.status === MirrorAiTaskStatus.SUCCESS && (
            <img
              src={publicPreLoadSourceObj.play}
              className="absolute top-2 left-2  w-[3.125rem] h-[3.125rem] cursor-pointer"
              onClick={handlePlay}
            />
          )}
          {curVideo === undefined && (
            <img
              src={publicPreLoadSourceObj.zoom}
              className="absolute top-2 left-2  w-[3.125rem] h-[3.125rem] cursor-pointer"
              onClick={handleImageClick}
            />
          )}
        </div>
      ) : curImg?.status === MirrorAiTaskStatus.FAIL ? (
        <>
          <div className="mb-2">
            <img
              src="/images/common/<EMAIL>"
              className="w-[64px] h-[64px]"
              alt=""
            />
          </div>
          <div>
            {t('生成失败')}
            <br />
            {t('请稍后重试')}
          </div>
        </>
      ) : (
        <div className="aspect-[2/3] flex flex-col items-center justify-center gap-16">
          {/* 排队中与生成中合并，统一展示进度条 */}
          <img
            src={publicPreLoadSourceObj.painting}
            className="w-[18.75rem] h-[18.75rem] phone:w-full phone:h-auto block mx-auto"
            alt=""
          />
          <div className="mb-2 text-white phone:text-[2.5rem]">
            {Math.floor((curImg?.generatingCompletePercent || 0) * 100)}%
          </div>
        </div>
      )}
    </div>
  )
}
