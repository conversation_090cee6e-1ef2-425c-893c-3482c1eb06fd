/** 活动页面 */
import { useEffect, useState } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { MirrorLoading } from 'wujieai-react-icon'
import {
  screenOrientationAtom,
  selectedEventIdAtom,
  selectedImageFrameAtom,
  selectedEventDetailAtom,
  modelsPerGenerationCountAtom,
  generationsPerModelCountAtom,
} from '@/stores'
import { usePreloadResource } from '@/hooks/usePreloadResource'

import { MyContainer } from '@/components/ui/MyContainer'
import { useTranslation } from 'react-i18next'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { useDevice } from '@/hooks/useDevice'
import { useMerchant } from '@/hooks/useMerchant'
import { useIsMobile } from '@/hooks/useIsMobile'
import { Direction } from '@/graphqls/types'
import EventCard from '@/components/pages/event/EventCard'
import classNames from 'classnames'
import { EventItem } from '@/apis/types'
import { useNavigate } from 'react-router-dom'
import StartPage from './start'
import { isWebApp } from '@/utils'
import { produce } from 'immer'

function EventPage() {
  const [loading, setLoading] = useState(false)
  const [selectLoading, setSelectLoading] = useState(false)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [selectedEventId, setSelectedEventId] = useAtom(selectedEventIdAtom)
  const [, setSelectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [, setSelectedFrame] = useAtom(selectedImageFrameAtom)
  const [, setModelsPerGenerationCount] = useAtom(modelsPerGenerationCountAtom)
  const [, setGenerationsPerModelCount] = useAtom(generationsPerModelCountAtom)
  const [eventList, setEventList] = useState<EventItem[]>([])
  const { getDefaultDeviceInfo } = useDevice()
  const { getDefaultMerchantInfo } = useMerchant()
  const { fetchResources } = usePreloadResource()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const isMobile = useIsMobile()

  useEffect(() => {
    getEventList()
  }, [])

  const getEventList = async () => {
    setLoading(true)
    const res = await _ajax.get(_api.event_list)
    const data = res?.data?.data?.data
    setEventList(data)
    setLoading(false)
  }

  const selectedEvent = async (eventId: number) => {
    if (!eventId) {
      console.log('EventId is required')
      return
    }
    setSelectLoading(true)
    const deviceInfo = await getDefaultDeviceInfo()
    const merchantInfo = await getDefaultMerchantInfo()
    localStorage.setItem('userChooseEventId', String(eventId))
    const direction =
      window.innerHeight > window.innerWidth
        ? Direction.VERTICAL
        : Direction.CROSSWISE
    const res = await _ajax.post(_api.register_device, {
      event_id: eventId,
      device_id: deviceInfo?.id,
      direction,
      merchant_no: merchantInfo?.no,
    })
    setSelectLoading(false)
    console.log('register_device with event', res)
  }

  const handleEventChoose = async (eventId: number, it: EventItem) => {
    const frameDetail = it?.event_frames?.[0]
    setSelectedEventId(eventId)
    setSelectedEventDetail({ ...it })
    setModelsPerGenerationCount(it.models_per_generation)
    setGenerationsPerModelCount(it.generations_per_model)
    if (frameDetail) {
      setSelectedFrame(frameDetail)
    }
    console.log('eventId', eventId)
    await selectedEvent(eventId)
    // await fetchResources(false)
    navigate('/silent')
  }

  if (isMobile && isWebApp()) {
    return <StartPage />
  }
  return (
    <>
      <MyContainer>
        <div className="px-24 py-6">
          <h1 className="maze-page-title mb-16">{t('选择活动')}</h1>
          {loading ? (
            <div className="text-center py-24">
              <MirrorLoading className="w-20 h-20 animate-spin maze-primary-text opacity-35" />
            </div>
          ) : (
            <div
              className={classNames(
                'grid gap-6',
                screenOrientation.isPortrait ? 'grid-cols-2' : 'grid-cols-4'
              )}
            >
              {eventList?.map((it, i) => (
                <a
                  className="block w-full h-[18.75rem]"
                  onClick={() => handleEventChoose(it?.id, it)}
                  key={it?.id || i}
                >
                  <EventCard
                    key={it?.id}
                    cardClassNames="h-full w-full"
                    data={it}
                    loading={selectLoading && it?.id === selectedEventId}
                  />
                </a>
              ))}
            </div>
          )}
        </div>
      </MyContainer>
    </>
  )
}

export default EventPage
