<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none">
  <foreignObject x="-10" y="-10" width="120" height="120"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_0_8336_49112_clip_path);height:100%;width:100%"></div></foreignObject><circle data-figma-bg-blur-radius="10" cx="50" cy="50" r="48.5" transform="rotate(-180 50 50)" fill="url(#paint0_linear_8336_49112)" fill-opacity="0.4" stroke="#EDF0F4" stroke-width="3"/>
  <path d="M39.5273 73.8086L63.0976 50.2384L39.5273 26.6681" stroke="white" stroke-width="7.14286" stroke-linecap="round" stroke-linejoin="round"/>
  <defs>
    <clipPath id="bgblur_0_8336_49112_clip_path" transform="translate(10 10)"><circle cx="50" cy="50" r="48.5" transform="rotate(-180 50 50)"/>
  </clipPath><linearGradient id="paint0_linear_8336_49112" x1="50" y1="-3.8147e-06" x2="50" y2="100" gradientUnits="userSpaceOnUse">
  <stop stop-color="#904DDD"/>
  <stop offset="1" stop-color="#ABC9E3"/>
</linearGradient>
</defs>
</svg>