import React, { useRef, useEffect } from 'react'
import { useSpring, animated } from '@react-spring/web'
import classNames from 'classnames'
import styles from './AutoScroll.module.css'
import Icon_right from '/images/icons/arrow-right.svg'
import Icon_left from '/images/icons/arrow-left.svg'

export const AutoScroll = ({
  /** 选中的tab序 */
  activeIndex = 0,
  /** 选中的行，支持点击单个元素，整个容器同时滚动 */
  activeRow = 0,
  className,
  wrapClassName,
  /** 节点，<div>{list.map}</div>结构，否则滚动不生效 */
  children,
  /** 存在多列情况 */
  isMultiple,
}: {
  activeIndex: number
  activeRow?: number
  className?: string
  wrapClassName?: string
  isMultiple?: boolean
  children?: React.ReactNode
}) => {
  const tabListContainerRef = useRef<HTMLDivElement>(null)

  const [{ scrollLeft }, scrollApi] = useSpring(() => ({
    scrollLeft: 0,
    config: {
      tension: 300,
      clamp: true,
    },
  }))

  const clamp = (
    position: number,
    min: number | undefined,
    max: number | undefined
  ) => {
    let ret = position
    if (min !== undefined) {
      ret = Math.max(position, min)
    }
    if (max !== undefined) {
      ret = Math.min(ret, max)
    }
    return ret
  }

  const animateHorizontal = () => {
    const container = tabListContainerRef.current
    if (!container) return
    const tabWrapper = container.children.item(activeRow) as HTMLDivElement
    if (!tabWrapper) return
    let activeTab: HTMLDivElement
    if (isMultiple) {
      activeTab = tabWrapper.children
        .item(0)
        ?.children.item(activeIndex) as HTMLDivElement
    } else {
      activeTab = tabWrapper.children.item(activeIndex) as HTMLDivElement
    }

    if (!activeTab) return
    /**选中tab的offsetLeft */
    const activeTabLeft = activeTab.offsetLeft
    /**选中tab的宽度 */
    const activeTabWidth = activeTab.offsetWidth
    /**容器有效宽度 */
    const containerWidth = container.offsetWidth
    /**容器总宽度（含超出部分） */
    const containerScrollWidth = container.scrollWidth
    /**容器scroll的距离 */
    const containerScrollLeft = container.scrollLeft
    /**最大可以滚动的距离 = overflow超出部分 */
    const maxScrollDistance = containerScrollWidth - containerWidth

    if (maxScrollDistance <= 0) return

    const nextScrollLeft = clamp(
      activeTabLeft - (containerWidth - activeTabWidth) / 2,
      0,
      maxScrollDistance
    )
    scrollApi.start({
      scrollLeft: nextScrollLeft,
      from: { scrollLeft: containerScrollLeft },
    })
  }

  useEffect(() => {
    // 避免取消选中时，回滚到第一屏
    if (activeIndex >= 0) {
      animateHorizontal()
    }
  }, [activeIndex, activeRow])

  return (
    <div className={classNames(styles.autoScrollWrap, wrapClassName)}>
      <animated.div
        className={classNames(styles.autoScroll, className)}
        ref={tabListContainerRef}
        scrollLeft={scrollLeft}
      >
        {children}
      </animated.div>
    </div>
  )
}

export const AutoScrollArrow = ({
  canPrev,
  canNext,
  onPrev,
  onNext,
}: {
  canPrev: boolean
  canNext: boolean
  onPrev: () => void
  onNext: () => void
}) => {
  return (
    <>
      <div
        className={classNames(styles.switchArrow, 'prevButton', {
          disabled: !canPrev,
        })}
        onClick={() => {
          if (!canPrev) return
          onPrev()
        }}
      >
        <img className="w-24 h-24" src={Icon_left} alt="" />
      </div>
      <div
        className={classNames(styles.switchArrow, 'nextButton', {
          disabled: !canNext,
        })}
        onClick={() => {
          if (!canNext) return
          onNext()
        }}
      >
        <img className="w-24 h-24" src={Icon_right} alt="" />
      </div>
    </>
  )
}
