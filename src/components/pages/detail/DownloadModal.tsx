import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { DownloadQrCode } from '../result/DownloadQrCode'

export const DownloadModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  mazeImgUrl: string
}> = ({ open, setOpen, mazeImgUrl }) => {
  const { t } = useTranslation()
  const onCloseQuestion = () => {
    setOpen?.(false)
  }

  return (
    <MyModal
      open={open}
      width={600}
      content={
        <div className="flex-1 pt-6">
          <DownloadQrCode url={mazeImgUrl} />
          <p className="py-12 text-[32px] text-center font-semibold leading-[42px]">
            {t('扫描二维码下载图片')}
          </p>
        </div>
      }
      onOk={onCloseQuestion}
      okText={t('完成')}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
