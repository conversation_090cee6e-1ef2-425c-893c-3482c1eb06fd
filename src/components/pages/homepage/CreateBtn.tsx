import { Button } from '@/components/ui/shad/button'
import { useToast } from '@/components/ui/shad/use-toast'
import {
  AiTaskType,
  DrawItemFragment,
  PrintTemplateSize,
} from '@/graphqls/types'
import { useDebounce } from '@/hooks/useDebounce'
import { usePayOrder } from '@/hooks/usePayOrder'
import { useValidateDevice } from '@/hooks/useValidateDevice'
import { useValidResourceChange } from '@/hooks/useValidResourceChange'
import { ShootTipModal } from './ShootTipModal'
import {
  taskTypeAtom,
  imageNumAtom,
  isPaidVersionAtom,
  machineInfoAtom,
  printerEnableAtom,
  printPriceAtom,
  mazeDrawPayOrderAtom,
  modelsPerGenerationCountAtom,
} from '@/stores'
import { ActiveTemplateItem } from './const'
import { PrinterStatus } from '@/stores/types'
import { fenToYuan, graphQLErrorMessage } from '@/utils'
import { use<PERSON>tom, useAtomValue, useSetAtom } from 'jotai'
import { useMemo, useState } from 'react'
import { MirrorCamera } from 'wujieai-react-icon'
import ModelPaymentModal from '../payment/ModelPaymentModal'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export const CreateBtn: React.FC<{
  activeTemplate: ActiveTemplateItem | null | undefined
  isPortrait?: boolean
  activeGender?: string
}> = ({ activeTemplate, isPortrait, activeGender }) => {
  const [payOrderLoading, setPayOrderLoading] = useState(false)
  const [payModal, setPayModal] = useState(false)
  const [tipModalOpen, setTipModalOpen] = useState(false)

  const [printPrice] = useAtom(printPriceAtom)
  const [printerEnable] = useAtom(printerEnableAtom)
  const [isPaidVersion] = useAtom(isPaidVersionAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const [imageNum] = useAtom(imageNumAtom)
  const [taskType] = useAtom(taskTypeAtom)
  const setDrawPayOrder = useSetAtom(mazeDrawPayOrderAtom)
  const modelsPerGenerationCount = useAtomValue(modelsPerGenerationCountAtom)

  const { validatePrinter, validateCamera } = useValidateDevice()
  const { createDrawPayOrder } = usePayOrder()
  const { toast } = useToast()
  const { t } = useTranslation()

  const { refetchResource, isResourceChange, ResourceChangeModal } =
    useValidResourceChange()

  /** 打印价格 */
  const currentPrintPrice = useMemo(() => {
    // console.log(
    //   '配置打印价格printPrice',
    //   printPrice,
    //   'printers',
    //   machineInfo.printers,
    //   'printerEnable',
    //   printerEnable
    // )
    // 无打印机情况
    let showPrintPrice = 0
    // 后台设置无打印机
    if (!printerEnable) return showPrintPrice

    // 有打印机情况
    if (
      machineInfo.printers?.some(
        it =>
          it.type === PrintTemplateSize.SIX_INCHES &&
          it.printerStatus === PrinterStatus.AVAILABLE
      )
    ) {
      // A6打印价格 (只要有一台A6)
      showPrintPrice = printPrice?.sixPrice || 0
    } else if (
      machineInfo.printers?.some(
        it =>
          it.type === PrintTemplateSize.A_FOUR &&
          it.printerStatus === PrinterStatus.AVAILABLE
      )
    ) {
      // A4打印价格
      showPrintPrice = printPrice?.a4Price || 0
    }
    return showPrintPrice
  }, [machineInfo.printers, printPrice, printerEnable])
  // console.log(
  //   '打印价格：',
  //   currentPrintPrice,
  //   '模板作画价',
  //   activeTemplate?.price
  // )
  /** 下单前逻辑处理 */
  const handlePrePayOrder = useDebounce(async () => {
    /** 备注：视频作画不支持上传图片 */
    if (taskType === AiTaskType.VIDEO) {
      if (!validateCamera()) return
    }
    /** 备注：作画不支持上传图片 */
    if (taskType === AiTaskType.DRAW) {
      if (!validatePrinter()) return
    }

    try {
      setPayOrderLoading(true)
      // const themeDetailData = await _ajax.get(_api.theme_detail, {
      //   params: { id: activeTemplate?.id },
      // })

      // // Get all items from the theme detail
      // let themeDetail = themeDetailData?.data?.data
      //   ?.itemList as DrawItemFragment[]

      // // Filter by gender if activeGender is provided
      // if (activeGender) {
      //   themeDetail = themeDetail.filter(item => item.sex === activeGender)
      // }

      // // Slice to get the required number of items
      // themeDetail = themeDetail.slice(0, modelsPerGenerationCount)

      // console.log(
      //   'create order',
      //   themeDetail,
      //   'filtered by gender:',
      //   activeGender
      // )

      // const orders = await Promise.all(
      //   themeDetail?.map(it => {
      //     return createDrawPayOrder({
      //       activeTemplate: it,
      //       printPrice: currentPrintPrice,
      //     })
      //   })
      // )
      // setDrawPayOrder(orders)

      /** 标准版且有价格的，为存在实际支付 */
      // setIsRealPay(isPaidVersion && drawOrderInfo.order?.totalFee > 0)

      // setCanShowVideo(false)

      // if (drawOrderInfo.order?.status === OrderStatus.UNPAID) {
      //   setPayModal(true)
      // } else {
      //   setTipModalOpen(true)
      //   // navigator('/photo')
      // }
      setTipModalOpen(true)
    } catch (error) {
      if (isResourceChange(error)) {
        await refetchResource()
      } else {
        toast({
          description: graphQLErrorMessage(error) || '下单失败，请稍后重试',
        })
      }
    } finally {
      setPayOrderLoading(false)
    }
  }, [activeTemplate, machineInfo, printerEnable, activeGender])

  return (
    <>
      <div className="flex flex-col items-center absolute z-10 left-[50%] translate-x-[-50%] bottom-10">
        {activeTemplate ? (
          <>
            <Button
              size="lg"
              className={classNames(
                'flex maze-bg-gradient-btn w-[29.32rem] h-[9.375rem] text-[3.125rem] rounded-[150px] ',
                'ipad:w-[24rem] ipad:h-[8rem] ipad:text-[2.5rem]'
              )}
              ga-data="preOrder"
              variant="outline"
              onClick={() => {
                handlePrePayOrder()
              }}
              loading={payOrderLoading}
            >
              {payOrderLoading ? (
                ''
              ) : (
                <>
                  {isPaidVersion && (
                    <>
                      <span className="mr-2 mt-1">$</span>
                      <span className="mr-6 text-xl">
                        {fenToYuan(activeTemplate.price + currentPrintPrice)}
                      </span>
                    </>
                  )}
                  <MirrorCamera size={70} className="btn-text-color mr-2" />
                  {t('立即开拍')}
                </>
              )}
            </Button>
            {isPaidVersion && taskType === AiTaskType.DRAW && (
              <div className="text-base text-neutral-50 mt-4 opacity-[0.56]">
                {printerEnable &&
                machineInfo?.printers?.some(
                  it => it.printerStatus === PrinterStatus.AVAILABLE
                )
                  ? `* 此价格包含 ${imageNum} 张 AI 图片以及 1 张打印费用`
                  : `* 此价格包含 ${imageNum} 张 AI 图片`}
              </div>
            )}
          </>
        ) : (
          <Button size="lg" className="flex opacity-[0.56]">
            {t('当前分类下暂无模板')}
          </Button>
        )}
      </div>
      <ShootTipModal
        open={tipModalOpen}
        setOpen={value => setTipModalOpen(value)}
      />
      <ModelPaymentModal
        visible={payModal}
        setVisible={setPayModal}
        activeTemplate={activeTemplate}
        printPrice={currentPrintPrice}
        isPortrait={isPortrait}
      />
      <ResourceChangeModal />
    </>
  )
}
