import { CDNImage } from '@/components/ui/CDNImage'
// import { useTranslation } from 'react-i18next'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'
import { ScreenOrientation } from '@/stores/types'
import { isIphone } from '@/utils'

export const PhotoMask: React.FC<{
  multiple: boolean
  className?: string
  direction?: ScreenOrientation
}> = ({ multiple, className }) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  // const { t } = useTranslation()
  if (isIphone()) {
    // 缺少素材，暂时隐藏
    return null
  }
  return (
    <>
      <CDNImage
        className={`absolute left-0 bottom-0 right-0 top-0 rounded-none object-cover ${className}`}
        src={
          multiple
            ? '/images/photo/mask-multiple.png'
            : screenOrientation.isLandScape
              ? '/images/photo/mask.png'
              : '/images/photo/mask-v.png'
        }
      />
      {/* <div className="flex justify-center absolute left-0 right-0 bottom-9">
        <div className="text-center font-bold text-base text-neutral-900 py-2 px-4 bg-[rgba(0,0,0,.5)] rounded-[8px]">
          {t('请保持人物主体在虚线框内')}
          <br />
          {t('以获得最佳生成效果')}
        </div>
      </div> */}
    </>
  )
}
