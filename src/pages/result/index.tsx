/**
 * 生成结果页
 */
import { useAtom } from 'jotai'
import {
  machineInfoAtom,
  printerEnableAtom,
  isRealPayAtom,
  taskTypeAtom,
} from '@/stores'
import { useNavigate } from 'react-router-dom'
import BackToHome from '@/components/business/BackToHome'
import { MyContainer } from '@/components/ui/MyContainer'
import { MyModal } from '@/components/ui/MyModal'
import { useState, useMemo } from 'react'
import { PrinterStatus } from '@/stores/types'
import { MazePictureResult } from '@/components/pages/result/MazePictureResult'
import { VideoResult } from '@/components/pages/result/VideoResult'
import { AiTaskType } from '@/graphqls/types'
import { useTranslation } from 'react-i18next'

function Result() {
  const [taskType] = useAtom(taskTypeAtom)
  const [backOpen, setBackOpen] = useState(false)
  const navigator = useNavigate()
  const [isRealPay] = useAtom(isRealPayAtom)
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)

  const { t } = useTranslation()

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])

  return (
    <>
      <BackToHome
        onShowDialog={() => true}
        dialogTitle={t('提示')}
        dialogContent={
          <div className="text-center leading-[180%]">
            <div>{t('您确认返回首页吗？')}</div>
            {hasPrinter && isRealPay && (
              <div>{t('返回后本次流程结束且不予退款')}</div>
            )}
          </div>
        }
      />
      <MyContainer>
        {taskType === AiTaskType.DRAW ? (
          <MazePictureResult setBackOpen={setBackOpen} />
        ) : (
          <VideoResult setBackOpen={setBackOpen} />
        )}
      </MyContainer>
      <MyModal
        open={backOpen}
        title=""
        content={t('您确认返回首页吗？')}
        cancelText={t('取消')}
        okText={t('确认')}
        onCancel={() => {
          setBackOpen(false)
        }}
        onOk={() => {
          navigator('/')
        }}
      />
    </>
  )
}

export default Result
