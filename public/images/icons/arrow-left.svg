<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none">
  <foreignObject x="-10" y="-10" width="120" height="120"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5px);clip-path:url(#bgblur_0_8336_49109_clip_path);height:100%;width:100%"></div></foreignObject><circle data-figma-bg-blur-radius="10" cx="50" cy="50" r="48.5" fill="url(#paint0_linear_8336_49109)" fill-opacity="0.4" stroke="#EDF0F4" stroke-width="3"/>
  <path d="M60.4727 26.1914L36.9024 49.7616L60.4727 73.3319" stroke="white" stroke-width="7.14286" stroke-linecap="round" stroke-linejoin="round"/>
  <defs>
    <clipPath id="bgblur_0_8336_49109_clip_path" transform="translate(10 10)"><circle cx="50" cy="50" r="48.5"/>
  </clipPath><linearGradient id="paint0_linear_8336_49109" x1="50" y1="0" x2="50" y2="100" gradientUnits="userSpaceOnUse">
  <stop stop-color="#904DDD"/>
  <stop offset="1" stop-color="#ABC9E3"/>
</linearGradient>
</defs>
</svg>