import { CDNImage } from '@/components/ui/CDNImage'
import { DownloadQrCode } from './DownloadQrCode'
import { useAtom, useAtomValue } from 'jotai'
import {
  taskTypeAtom,
  screenOrientationAtom,
} from '@/stores'
import classNames from 'classnames'
import { AiTaskType } from '@/graphqls/types'
import { useTranslation } from 'react-i18next'
type Props = {
  className?: string
  notice?: React.ReactNode
  qrCodeClassName?: string
  qrCodeSize?: string
}
const ResultQrcodeComp = ({
  className,
  qrCodeClassName,
  qrCodeSize,
  notice,
}: Props) => {
  const [taskType] = useAtom(taskTypeAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { t } = useTranslation()

  return (
    <div
      className={classNames(
        className,
        screenOrientation.isPortrait && 'flex items-center'
      )}
    >
      <div
        className={classNames(
          'bg-text-color text-[32px] text-center font-bold',
          screenOrientation.isLandScape && 'mb-12'
        )}
      >
        {notice ? (
          notice
        ) : taskType === AiTaskType.DRAW ? (
          <>
            <div className="flex items-center justify-center mb-2 ">
              {/* <CDNImage
                src="/images/common/wechat_round.png"
                alt=""
                className="w-[48px] h-[48px] mr-2"
              /> */}
              <span>{t('微信扫码')}</span>
            </div>
            <h3 className="mb-2">{t('领取所有电子照片')}</h3>
          </>
        ) : (
          <div className="flex items-center justify-center mb-2">
            <CDNImage
              src="/images/common/wechat_round.png"
              alt=""
              className="w-[48px] h-[48px] mr-2"
            />
            <span>{t('微信扫码领取视频')}</span>
          </div>
        )}
      </div>
      <div className={classNames('px-6', qrCodeClassName)}>
        <DownloadQrCode size={qrCodeSize} />
      </div>
    </div>
  )
}
export default ResultQrcodeComp
