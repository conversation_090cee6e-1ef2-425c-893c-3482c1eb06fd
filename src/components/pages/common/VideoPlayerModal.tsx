import { MyModal } from '@/components/ui/MyModal'
import { MyImage } from '@/components/ui/MyImage'
import {
  VideoPlayer,
  VideoPlayerRef,
} from '@/components/pages/result/VideoPlayer'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import { useEffect, useRef } from 'react'
import {
  screenOrientationAtom,
  globalVideoPlayerAtom,
  MediaType,
} from '@/stores'
import { useAtom, useAtomValue } from 'jotai'
import { isPad } from '@/utils'

/**
 * 全局媒体播放器弹窗组件
 * 支持显示视频和图片，使用 jotai 全局状态管理，不再需要 props
 */
export function VideoPlayerModal() {
  const [globalVideoPlayer, setGlobalVideoPlayer] = useAtom(
    globalVideoPlayerAtom
  )
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const videoPlayerRef = useRef<VideoPlayerRef>(null)

  // 解构全局状态
  const { open, mediaType, video, imageUrl, poster } = globalVideoPlayer

  // 关闭弹窗的方法
  const handleClose = () => {
    setGlobalVideoPlayer(prev => ({ ...prev, open: false }))
  }

  // 当弹窗打开且为视频模式时自动播放视频
  useEffect(() => {
    if (open && mediaType === MediaType.VIDEO) {
      console.log('🎬 VideoPlayerModal: 弹窗打开，准备自动播放视频')

      // 使用更长的延迟确保video元素已经渲染，并添加重试机制
      const attemptPlay = (retryCount = 0) => {
        if (videoPlayerRef.current) {
          console.log('🎬 VideoPlayerModal: 找到video ref，尝试播放')
          videoPlayerRef.current.play()
        } else if (retryCount < 5) {
          console.log(
            `🎬 VideoPlayerModal: video ref未就绪，${200 * (retryCount + 1)}ms后重试 (${retryCount + 1}/5)`
          )
          setTimeout(() => attemptPlay(retryCount + 1), 200 * (retryCount + 1))
        } else {
          console.error('🎬 VideoPlayerModal: 重试5次后仍无法找到video ref')
        }
      }

      // 初始延迟300ms后开始尝试
      const timer = setTimeout(() => attemptPlay(), 300)
      return () => clearTimeout(timer)
    }
  }, [open, mediaType])

  // 当弹窗关闭时停止视频播放
  useEffect(() => {
    if (!open && videoPlayerRef.current && mediaType === MediaType.VIDEO) {
      console.log('🎬 VideoPlayerModal: 弹窗关闭，停止视频播放')
      videoPlayerRef.current.pause()
    }
  }, [open, mediaType])

  // 如果是视频模式但视频不存在或未成功，不显示
  if (
    mediaType === MediaType.VIDEO &&
    (!video || video.status !== MirrorAiTaskStatus.SUCCESS)
  ) {
    return null
  }

  // 如果是图片模式但图片URL不存在，不显示
  if (mediaType === MediaType.IMAGE && !imageUrl) {
    return null
  }

  // 如果没有指定媒体类型，不显示
  if (!mediaType) {
    return null
  }

  return (
    <MyModal
      open={open}
      onCancel={handleClose}
      width={screenOrientation.isLandScape ? 490 : isPad() ? '66vw' : '86vw'}
      className="p-0 bg-neutral-900 border-none max-h-[90vh]"
      contentClassName="!p-0 border-none"
      footer={null}
      content={
        <div className="w-full h-full relative">
          {mediaType === MediaType.VIDEO && video ? (
            <VideoPlayer
              ref={videoPlayerRef}
              videoUrl={video.resultUrl!}
              poster={poster || ''}
              id={`video-modal-${video.id}`}
            />
          ) : mediaType === MediaType.IMAGE && imageUrl ? (
            <MyImage
              src={imageUrl}
              className="w-full h-full rounded-2xl"
              imgClassName="object-contain"
              tag="v1200"
            />
          ) : null}
        </div>
      }
    />
  )
}
