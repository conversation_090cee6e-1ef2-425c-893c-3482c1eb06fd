diff --git a/node_modules/conventional-commit-types/index.json b/node_modules/conventional-commit-types/index.json
index 8bc4c79..b04b624 100644
--- a/node_modules/conventional-commit-types/index.json
+++ b/node_modules/conventional-commit-types/index.json
@@ -1,47 +1,55 @@
 {
   "types": {
     "feat": {
-      "description": "A new feature",
+      "description": "新的功能",
       "title": "Features"
     },
+    "opt": {
+      "description": "优化 （样式、功能、细节）",
+      "title": "Optimization"
+    },
     "fix": {
-      "description": "A bug fix",
+      "description": "修复 bug",
       "title": "Bug Fixes"
     },
     "docs": {
-      "description": "Documentation only changes",
+      "description": "只修改文档",
       "title": "Documentation"
     },
     "style": {
-      "description": "Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)",
+      "description": "不影响代码含义的修改（比如：空格、格式化、添加缺少的分号等）",
       "title": "Styles"
     },
     "refactor": {
-      "description": "A code change that neither fixes a bug nor adds a feature",
+      "description": "重构代码（既不修复错误，也不增加功能）",
       "title": "Code Refactoring"
     },
     "perf": {
-      "description": "A code change that improves performance",
+      "description": "提高性能",
       "title": "Performance Improvements"
     },
     "test": {
-      "description": "Adding missing tests or correcting existing tests",
+      "description": "添加测试或纠正现有测试",
       "title": "Tests"
     },
     "build": {
-      "description": "Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)",
+      "description": "影响构建系统或外部依赖的变化（如 glup、npm 等）",
       "title": "Builds"
     },
     "ci": {
-      "description": "Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)",
+      "description": "ci 配置文件和脚本的改变 （如：Travis、Circle）",
       "title": "Continuous Integrations"
     },
     "chore": {
-      "description": "Other changes that don't modify src or test files",
+      "description": "其它不修改 src 或测试文件的改动（如：各种配置文件修改，build 类型的除外）",
       "title": "Chores"
     },
     "revert": {
-      "description": "Reverts a previous commit",
+      "description": "回滚之前的提交",
+      "title": "Reverts"
+    },
+    "interval": {
+      "description": "正式提交diff之前的commit（如：模块功能没开发完成，但需要commit）",
       "title": "Reverts"
     }
   }
