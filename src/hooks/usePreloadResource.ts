import { useRef, useState } from 'react'
import {
  CategoryListFragment,
  Direction,
  GetResourcesDocument,
  GetResourcesQuery,
  GetResourcesQueryVariables,
  ResourcePackageFragment,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useAtomValue, useSetAtom } from 'jotai'
import {
  imageNumAtom,
  printPriceAtom,
  resourceHashAtom,
  resourceTemplateAtom,
  shotNumAtom,
  silentConfigAtom,
  isUseCrowdAtom,
  isSupportVideoAtom,
  resourceVideoTemplateAtom,
  photoAlbumQrcodeAtom,
  selectedEventDetailAtom,
} from '@/stores'
import {
  graphQLErrorCode,
  preLoadImages,
  preLoadVideos,
  to800Image,
  validateIsVideo,
} from '@/utils'
import { publicPreLoadVideos, publicPreLoadImages } from '@/configs/source'
import { useDevice } from '@/hooks/useDevice'
import { useThemeConfig } from './useThemeConfig'
import { useMerchant } from './useMerchant'
import { supportAppPreloadResource } from '@/configs'
import { useTranslation } from 'react-i18next'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { VIRTUAL_UID } from '@/configs'
import _api from '@/apis/maze.api'
import _ajax from '@/utils/ajax'
import { ThemeList, ThemeDetail } from '@/apis/types'

/** 预加载资源 */
export const usePreloadResource = () => {
  const [loadStatus, setLoadStatus] = useState<
    'loading' | 'success' | 'failed' | 'undistributed'
  >('loading')
  const loadedRef = useRef(false)
  const [errorText, setErrorText] = useState('')

  const setResourceHash = useSetAtom(resourceHashAtom)
  const setShotNum = useSetAtom(shotNumAtom)
  const setImageNum = useSetAtom(imageNumAtom)
  const setResourceTemplate = useSetAtom(resourceTemplateAtom)
  const setResourceVideoTemplate = useSetAtom(resourceVideoTemplateAtom)
  const setPrintPrice = useSetAtom(printPriceAtom)
  const setSilentConfig = useSetAtom(silentConfigAtom)
  const setIsUseCrowdAtom = useSetAtom(isUseCrowdAtom)
  const setIsSupportVideo = useSetAtom(isSupportVideoAtom)
  const setPhotoAlbumQrcode = useSetAtom(photoAlbumQrcodeAtom)
  const selectedEventDetail = useAtomValue(selectedEventDetailAtom)
  const navigator = useNavigate()

  const { t } = useTranslation()

  const {
    getDeviceInfo,
    injectDeviceToken,
    toAppPreloadResource,
    getTokenByUid,
  } = useDevice()
  const { getMerchantInfo } = useMerchant()
  const { setThemeConfig } = useThemeConfig()
  const [searchParam] = useSearchParams()

  const getResources = useImperativeQuery<
    GetResourcesQuery,
    GetResourcesQueryVariables
  >(GetResourcesDocument)

  // maze theme
  const getThemes = async () => {
    return new Promise(
      (resolve: (res: ThemeList) => void, reject: (err: any) => void) => {
        _ajax
          .get(_api.themes)
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      }
    )
  }

  // 静默页面资源
  const silentResource = (resourcePackage: ResourcePackageFragment) => {
    // 横版资源包静默资源
    if (resourcePackage.direction === Direction.CROSSWISE) {
      return {
        silentVideos: (resourcePackage.silencePage?.filter(
          it => !!it && validateIsVideo(it)
        ) || []) as string[],
        silentImages:
          resourcePackage.silencePage
            ?.filter(it => !!it && !validateIsVideo(it))
            ?.map(it => to800Image(it!)) || [],
      }
    }
    // 竖版资源包静默资源
    return {
      silentVideos: (resourcePackage.verticalSilencePage?.filter(
        it => !!it && validateIsVideo(it)
      ) || []) as string[],
      silentImages:
        resourcePackage.verticalSilencePage
          ?.filter(it => !!it && !validateIsVideo(it))
          ?.map(it => to800Image(it!)) || [],
    }
  }

  const preloadAllResource = async (
    resourcePackage: ResourcePackageFragment
  ) => {
    const allImages = new Set<string>()
    const allVideos = new Set<string>()
    const { silentVideos, silentImages } = silentResource(resourcePackage)
    if (resourcePackage?.categoryList) {
      resourcePackage?.categoryList?.forEach(item => {
        if (item?.itemList) {
          // item?.itemList?.forEach(it => {
          // allImages.add(it?.image as string)
          // it?.exampleImage?.forEach(example => {
          // allImages.add(example as string)
          // })
          // })
        }
      })
    }

    if (resourcePackage?.categoryVideoList) {
      resourcePackage?.categoryVideoList?.forEach(item => {
        if (item?.itemList) {
          // item?.itemList?.forEach(it => {
          // allImages.add(it?.image as string)
          // allVideos.add(it?.coverUrl as string)
          // })
        }
      })
    }

    const videos = Array.from(allVideos)
      .concat(publicPreLoadVideos)
      .concat(silentVideos)
    const imgs = Array.from(allImages)
      .map(url => to800Image(url))
      .concat(publicPreLoadImages)
      .concat(silentImages)

    if (supportAppPreloadResource()) {
      await toAppPreloadResource([...imgs, ...videos])
    } else {
      await preLoadImages(imgs)
      await preLoadVideos(videos)
    }
  }
  const fetchResources = async (preloadResource?: boolean) => {
    setLoadStatus('loading')

    try {
      const linkVirTualUid = searchParam.get(VIRTUAL_UID)
      if (linkVirTualUid) {
        await getTokenByUid()
      } else {
        // 大屏设备参数携带
        injectDeviceToken()
      }
    } catch (error) {
      console.log('error', error)
      setErrorText(t('您访问的链接无效，请检查链接是否正确'))
      setLoadStatus('failed')
      return
    }

    try {
      // 获取 资源包 以及 后台配置的设备信息
      const [resourceRes] = await Promise.all([
        getResources(),
        getDeviceInfo(),
        getMerchantInfo(),
      ])
      // 获取主题
      const themes = await getThemes()
      const themeList = themes?.data?.data
      const resourcePackage =
        resourceRes?.data?.mirrorMarketQuery?.resourcePackage
      setResourceHash(resourcePackage?.hash || '')
      setShotNum(resourcePackage?.photograph?.shotNum || 0)
      setImageNum(resourcePackage?.imageNum || 1)
      setIsSupportVideo(!!resourcePackage?.videoOpen)

      setSilentConfig({
        images: (resourcePackage?.silencePage as string[]) || [],
        portraitImages:
          (resourcePackage?.verticalSilencePage as string[]) || [],
        timer: (resourcePackage?.rotationCycle || 10) * 1000,
      })

      setThemeConfig({
        bgColor: resourcePackage?.bgColor, // 背景色
        bgUrl: resourcePackage?.bgUrl, // 横版背景图
        portraitBgUrl: resourcePackage?.verticalBgUrl, // 竖版背景图
        textColor: resourcePackage?.textColor, // 背景下的文字颜色
        themeColor: resourcePackage?.themeColor, // 主题色/按钮背景色
        buttonTextColor: resourcePackage?.buttonTextColor, // 按钮文字颜色
        logoUrl: resourcePackage?.logoUrl,
      })
      setPhotoAlbumQrcode(resourcePackage?.receiveQRCode)
      setResourceTemplate(themeList as ThemeDetail[])
      setResourceVideoTemplate(
        resourcePackage?.categoryVideoList as CategoryListFragment[]
      )
      setPrintPrice(
        (resourcePackage?.printPriceInfo as {
          /** 6寸打印价格 */
          sixPrice: number
          /** 4寸打印价格 */
          a4Price: number
        }) || {
          sixPrice: 0,
          a4Price: 0,
        }
      )

      setIsUseCrowdAtom(resourcePackage?.classificationMode === 1)

      if (preloadResource) {
        await preloadAllResource(resourcePackage as ResourcePackageFragment)
      }
      setLoadStatus('success')
      loadedRef.current = true
    } catch (error) {
      console.log('error', error)
      if (graphQLErrorCode(error) === 21060001) {
        setLoadStatus('undistributed')
        setErrorText(t('未检测到资源包，请联系运营人员进行配置以继续使用'))
        return
      } else {
        setErrorText(t('获取资源包失败，请重试'))
      }
      setLoadStatus('failed')
    }
  }

  return {
    /** 获取资源包 */
    fetchResources,
    loadStatus,
    errorText,
    /** 是否加载完成 */
    loadedRef,
  }
}
