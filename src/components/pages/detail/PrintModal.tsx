import { useState, useMemo, useRef } from 'react'
import { useAtom, useAtomValue } from 'jotai'

import {
  drawPayOrderAtom,
  isPaidVersionAtom,
  machineInfoAtom,
  printPayOrderAtom,
  resultOrderAtom,
  resultImagesAtom,
  screenOrientationAtom,
} from '@/stores'
import {
  Direction,
  OrderStatus,
  PrintOrderInput,
  PrintOrderPayInfoFragment,
  PrintTemplateSize,
  useReportPrintSuccessMutation,
} from '@/graphqls/types'
import { graphQLErrorMessage, toRotateImage } from '@/utils'
import { MyModal } from '@/components/ui/MyModal'
import { PrinterStatus } from '@/stores/types'
import { useTranslation } from 'react-i18next'
import { MyMirrorAiTask } from '@/stores/types'
import MyStepper from '@/components/ui/MyStepper'
import { useDebounce } from '@/hooks/useDebounce'
import { usePayOrder } from '@/hooks/usePayOrder'
import { useValidResourceChange } from '@/hooks/useValidResourceChange'
import { PRINTER_TYPE } from '@/configs'
import { useBridge } from '@/hooks/useBridge'
import PrintPaymentModal from '@/components/pages/payment/PrintPaymentModal'
import {
  ErrorModal,
  LoadingModal,
  SuccessModal,
} from '@/components/pages/print/LoadingModal'
import { useToast } from '@/components/ui/shad/use-toast'

export const roateImage = (url?: string, direction?: Direction) => {
  if (direction === Direction.VERTICAL) return url
  return toRotateImage(url!)
}

export const PrintModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  curImg: MyMirrorAiTask | undefined
  mazeImgUrl: string
}> = ({ open, setOpen, curImg, mazeImgUrl }) => {
  const [count, setCount] = useState(1)

  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [resultImages, setResultImages] = useAtom(resultImagesAtom)
  const [resultOrder] = useAtom(resultOrderAtom)

  const [machineInfo] = useAtom(machineInfoAtom)
  const [printOrderInfo] = useAtom(printPayOrderAtom)
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [isPaidVersion] = useAtom(isPaidVersionAtom)
  const [isLoading, setIsLoading] = useState(false)

  /** 超时定时器恢复weappPrinting状态 */
  const timeoutRef = useRef<any>(null)

  /** 打印错误的childOrder */
  const [printErrorOrder, setPrintErrorOrder] =
    useState<PrintOrderPayInfoFragment>({})
  /** loading 弹窗 */
  const [loadingOpen, setLoadingOpen] = useState(false)
  /** 打印完成 */
  const [printDone, setPrintDone] = useState(false)
  /** 打印错误弹窗 */
  const [printError, setPrintError] = useState(false)
  /** 支付弹窗 */
  const [payOpen, setPayOpen] = useState(false)
  const { createPrintPayOrder } = usePayOrder()

  /** 免费打印次数是否已使用 */
  const [freePrintUsed, setFreePrintUsed] = useState(false)

  /** 错误失败重试次数 */
  const [retryCount, setRetryCount] = useState(0)

  const { t } = useTranslation()

  const {
    refetchResource,
    isResourceChange,
    ResourceChangeModal,
    isPrintOrderChange,
  } = useValidResourceChange()

  const { printImage } = useBridge()
  const { toast } = useToast()

  const [reportPrintSuccessAction] = useReportPrintSuccessMutation()

  const printImages = [curImg]
  //** 打印总数 */
  const printNums = count

  /** 创建订单参数 */
  const printOrderParam = useMemo((): PrintOrderInput => {
    return {
      price:
        (printNums - (freePrintUsed ? 0 : 1)) *
        resultOrder?.printPriceSnapshot?.sixPrice,
      printInfoList: printImages.map(it => {
        return {
          mirrorAiTaskId: it?.id,
          num: 1, // fixed  临时修复二次打印为 0 bug，后期移除createPrintPayOrder
          size: PrintTemplateSize.SIX_INCHES,
        }
      }),
      orderId: drawOrderInfo.order?.id,
    }
  }, [printNums, printImages, drawOrderInfo, freePrintUsed, resultOrder])

  const reportPrintStatus = async ({
    res,
    cbRes,
    fallbackFailMessage,
  }: {
    res: PrintOrderPayInfoFragment
    cbRes?: any
    fallbackFailMessage?: string
  }) => {
    const orderIds = res?.order?.printInfoList
      ?.map(it => it.childOrderId)
      ?.filter((_, i) => {
        return !!cbRes?.results?.[i]?.success
      })

    let failMessage = fallbackFailMessage
    // 如果有错误，上报
    if (cbRes?.results?.some((it: any) => !it.success)) {
      failMessage = JSON.stringify(
        cbRes?.results?.filter((it: any) => !it.success)
      )
    }

    await reportPrintSuccessAction({
      variables: {
        printOrderId: res?.order?.id,
        // 空数组上报表示全失败
        ids: orderIds?.length ? orderIds : [],
        failMessage,
      },
    }).catch(() => {})
    // 错误的记录 重新打印
    if (cbRes?.results?.some((it: any) => !it.success)) {
      setLoadingOpen(false)
      setPrintError(true)
      setPrintErrorOrder({
        ...res,
        order: {
          ...res.order,
          printInfoList: res?.order?.printInfoList?.filter((_, i) => {
            return !cbRes?.results?.[i]?.success
          }),
        },
      })
    } else {
      setTimeout(() => {
        // 全部成功
        setLoadingOpen(false)
        setPrintDone(true)
      }, 5000)
    }
  }

  const onPrint = useDebounce(async () => {
    try {
      console.log('printImages', printImages, printOrderParam)
      if (isLoading) return
      setIsLoading(true)
      // const res = await createPrintPayOrder({
      //   param: printOrderParam,
      // })
      // if (res.order?.status === OrderStatus.UNPAID) {
      //   setPayOpen(true)
      // } else {
      //   toPrint(res)
      // }
      toPrint({
        order: {
          id: '123',
          printInfoList: [
            {
              childOrderId: '123',
              mirrorAiTask: {
                id: '123',
              },
            },
          ],
        },
      })
    } catch (error: any) {
      if (isResourceChange(error)) {
        await refetchResource()
      } else if (isPrintOrderChange(error)) {
        setFreePrintUsed(true)
      } else {
        toast({
          description: graphQLErrorMessage(error) || t('下单失败，请稍后重试'),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [printOrderParam])

  const toPrint = async (res: PrintOrderPayInfoFragment) => {
    try {
      setLoadingOpen(true)
      try {
        printImage({
          content: new Array(count)?.fill(0)?.map(() => {
            // 直接打印maze合成相框后的img url
            return {
              imageUrl: roateImage(mazeImgUrl, Direction.VERTICAL),
              count: 1,
              type: PRINTER_TYPE[PrintTemplateSize.SIX_INCHES],
            }
          }),
          callback: async (cbRes: any) => {
            await reportPrintStatus({
              res,
              cbRes,
            })
            clearTimeout(timeoutRef.current)
          },
        })
        /** 兜底3分钟打印机不回调callback，能主动上报失败，并且弹窗提示*/
        timeoutRef.current = setTimeout(
          () => {
            reportPrintStatus({
              res,
              fallbackFailMessage: '前端：兜底3分钟打印机不回调',
            })
          },
          3 * 60 * 1000
        )
      } catch (error) {
        console.log(error, 'error')
      }
    } catch (error) {
      setLoadingOpen(false)
      setPrintError(true)
    }
  }

  const linkedPriner = useMemo(() => {
    return machineInfo.printers?.filter(
      it => it.printerStatus === PrinterStatus.AVAILABLE
    )?.[0]
  }, [machineInfo])
  /** 打印总数不超过打印纸 */
  const plusDisabled = useMemo(() => {
    return count >= (linkedPriner?.printerPaperNum ?? 0)
  }, [linkedPriner, count])

  console.log('print modal: ', resultImages, machineInfo, linkedPriner)

  return (
    <>
      <MyModal
        open={open}
        width={screenOrientation.isLandScape ? 600 : '90vw'}
        content={
          <div className="flex-1 pt-6">
            <MyStepper
              value={count}
              min={1}
              max={
                (linkedPriner?.printerPaperNum || 0) > 3
                  ? 3
                  : linkedPriner?.printerPaperNum
              }
              plusDisabled={plusDisabled}
              className="mx-6"
              onChange={value => {
                setCount(value)
              }}
            />
            <p className="py-12 text-[2rem] text-center font-semibold leading-[42px]">
              {t('请选择打印数量')}
            </p>
          </div>
        }
        onOk={onPrint}
        okText={t('立即打印')}
        onCancel={() => setOpen?.(false)}
        contentClassName="p-0 w-full"
      />
      <PrintPaymentModal
        visible={payOpen}
        setVisible={setPayOpen}
        onSuccess={() => {
          setPayOpen(false)
          toPrint(printOrderInfo)
        }}
        printOrderParam={printOrderParam}
      />
      <LoadingModal open={loadingOpen} setOpen={setLoadingOpen} />
      <ErrorModal
        open={printError}
        setOpen={setPrintError}
        onRetry={() => {
          toPrint(printErrorOrder)
          setRetryCount(count => count + 1)
        }}
        retryCount={retryCount}
      />
      <SuccessModal open={printDone} />
      <ResourceChangeModal />
    </>
  )
}
