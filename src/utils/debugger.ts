import eruda from 'eruda'
// import * as Sentry from '@sentry/react'
import { isIPad, isPad, isIphone, isPhone, isMachine, isWebApp } from '../utils'
import packageJson from '../../package.json'

export const initDebugger = () => {
  // 连续点击触发调试器功能
  let clickCount = 0
  let clickTimer: NodeJS.Timeout | null = null
  const CLICK_THRESHOLD = 10 // 需要连续点击的次数
  const CLICK_TIME_WINDOW = 300 // 时间窗口（毫秒）

  const handleDebugClick = () => {
    clickCount++

    // 清除之前的定时器
    if (clickTimer) {
      clearTimeout(clickTimer)
    }

    // 设置新的定时器，在时间窗口结束后重置计数
    clickTimer = setTimeout(() => {
      clickCount = 0
    }, CLICK_TIME_WINDOW)

    // 检查是否达到触发条件
    if (clickCount >= CLICK_THRESHOLD) {
      console.log(
        '%c🔧 调试模式已激活！',
        'color: #ff6b6b; font-weight: bold; font-size: 16px; background: #fff3cd; padding: 8px; border-radius: 4px;'
      )
      // 触发调试器
      eruda.init()
      // 重置计数
      clickCount = 0
      if (clickTimer) {
        clearTimeout(clickTimer)
        clickTimer = null
      }
      document.removeEventListener('click', handleDebugClick)
      document.removeEventListener('touchstart', handleDebugClick)
    }
  }

  // 添加全局点击监听器
  document.addEventListener('click', handleDebugClick)
  document.addEventListener('touchstart', handleDebugClick)

  // 输出应用基本信息
  console.log(
    '%c🚀 MAZE APP INFO',
    'color: #8072ED; font-weight: bold; font-size: 14px; padding: 4px 0;'
  )
  console.log(
    '%c📦 Version: %c%s',
    'color: #666; font-weight: 500;',
    'color: #2563eb; font-weight: bold;',
    packageJson.version
  )
  console.log(
    '%c🌍 Environment: %c%s',
    'color: #666; font-weight: 500;',
    'color: #2563eb; font-weight: bold;',
    import.meta.env.MODE
  )
  const windowW = document?.documentElement?.clientWidth
  const windowH = document?.documentElement?.clientHeight
  console.log(
    '%c🖥️ Screen Size: (width x height) %c%s x %s',
    'color: #666; font-weight: 500;',
    'color: #2563eb; font-weight: bold;',
    windowW,
    windowH
  )

  // 机器中的测试环境，打开调试
  const isTestEnv =
    window.location.hostname === 'mirror_test.mgru.info' ||
    window.location.hostname === 'mirror.wujiebantu.com' ||
    (/:16661/.test(window.location.href) &&
      (isIPad() ||
        isPad() ||
        isIphone() ||
        isPhone() ||
        isMachine() ||
        isWebApp()))

  if (isTestEnv) {
    eruda.init()
  }

  // Sentry.init({
  //   dsn: 'https://<EMAIL>/7',
  //   integrations: [
  //     Sentry.browserTracingIntegration(),
  //     Sentry.replayIntegration(),
  //   ],
  //   // Performance Monitoring
  //   tracesSampleRate: 1.0, //  Capture 100% of the transactions
  //   // Session Replay
  //   replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  //   replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  // })
}
